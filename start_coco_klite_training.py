#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 COCO子集 + YOLOv8-klite 知识蒸馏训练启动脚本
基于成功的fruit数据集配置，适配COCO数据集
"""

import os
import sys
import torch
import Globals
from ultralytics import YOLO

def main():
    print("🚀 启动COCO子集 + YOLOv8-klite 知识蒸馏训练")
    print("=" * 60)
    
    # 显示配置信息
    print(f"📁 模型文件: {Globals.model_file}")
    print(f"📊 数据集: {Globals.dataset}")
    print(f"🔄 训练轮数: {Globals.epochs}")
    print(f"📦 批次大小: {Globals.batch_size}")
    print(f"🎯 知识蒸馏: {'启用' if Globals.bool_distill else '禁用'}")
    print(f"🌡️ 温度参数: {Globals.hyp_T}")
    print(f"⚖️ 蒸馏权重: Box={Globals.hyp_box_distill}, Cls={Globals.hyp_cls_distill}, DFL={Globals.hyp_dfl_distill}")
    print("=" * 60)
    
    # 检查文件是否存在
    if not os.path.exists(Globals.model_file):
        print(f"❌ 错误: 模型文件不存在: {Globals.model_file}")
        return False
        
    if not os.path.exists(Globals.dataset):
        print(f"❌ 错误: 数据集文件不存在: {Globals.dataset}")
        return False
    
    # 检查GPU
    if torch.cuda.is_available():
        print(f"🎮 GPU设备: {torch.cuda.get_device_name(0)}")
        print(f"💾 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    else:
        print("⚠️ 警告: 未检测到GPU，将使用CPU训练")
    
    try:
        # 创建模型
        print("\n🔧 初始化模型...")
        model = YOLO(Globals.model_file)
        
        # 训练参数
        train_args = {
            'data': Globals.dataset,
            'epochs': Globals.epochs,
            'batch': Globals.batch_size,
            'device': Globals.device,
            'save': Globals.bool_save,
            'save_period': Globals.save_period,
            'resume': Globals.bool_resume,
            'project': 'runs/detect',
            'name': f'coco_klite_T{Globals.hyp_T}_ep{Globals.epochs}',
            'exist_ok': True,
            'pretrained': True,
            'optimizer': 'AdamW',
            'lr0': 0.001,
            'lrf': 0.01,
            'momentum': 0.937,
            'weight_decay': 0.0005,
            'warmup_epochs': 3.0,
            'warmup_momentum': 0.8,
            'warmup_bias_lr': 0.1,
            'box': 7.5,
            'cls': 0.5,
            'dfl': 1.5,
            'label_smoothing': Globals.label_smoothing,
            'val': True,
            'plots': True,
            'verbose': True,
            'seed': 42,
            'deterministic': True,
            'workers': 8,
            'amp': True,
            'fraction': 1.0,
            'profile': False,
        }
        
        print("🚀 开始训练...")
        print(f"📝 训练参数: {train_args}")
        
        # 开始训练
        results = model.train(**train_args)
        
        print("\n✅ 训练完成!")
        print(f"📈 训练结果: {results}")
        
        # 保存模型
        if hasattr(model, 'model'):
            torch.save(model.model.state_dict(), Globals.final)
            print(f"💾 模型已保存: {Globals.final}")
        
        # 验证模型
        print("\n🔍 验证模型...")
        val_results = model.val(data=Globals.dataset, device=Globals.device)
        
        print("📊 最终验证结果:")
        print(f"  🎯 mAP@0.5: {val_results.box.map50:.4f}")
        print(f"  📈 mAP@0.5:0.95: {val_results.box.map:.4f}")
        print(f"  🎯 Precision: {val_results.box.mp:.4f}")
        print(f"  📊 Recall: {val_results.box.mr:.4f}")
        
        # 性能评估
        if val_results.box.map50 > 0.5:
            print("🏆 优秀! COCO子集训练成功")
        elif val_results.box.map50 > 0.3:
            print("👍 良好! 可以扩展到更大数据集")
        else:
            print("⚠️ 需要检查配置或数据集")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 COCO子集知识蒸馏训练完成!")
        print("📁 可以查看训练结果在 runs/detect/ 目录下")
        print("🔄 如需扩展到完整COCO数据集，请修改数据集配置文件")
    else:
        print("\n❌ 训练失败，请检查配置和错误信息")
        sys.exit(1)
