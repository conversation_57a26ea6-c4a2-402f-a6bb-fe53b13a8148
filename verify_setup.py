#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 验证知识蒸馏配置 - 确保所有组件正确设置
"""

import os
import sys
import torch
from pathlib import Path

def check_files():
    """检查必要文件是否存在"""
    print("📁 检查文件存在性...")
    
    files_to_check = [
        "Globals.py",
        "ultralytics/cfg/models/v8/yolov8-lite-fruit.yaml",
        "ultralytics/cfg/datasets/fruit.yaml",
        "ultralytics/utils/loss.py",
        "train_enhanced_distillation.py"
    ]
    
    all_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} - 文件不存在!")
            all_exist = False
    
    return all_exist

def check_globals_config():
    """检查Globals.py配置"""
    print("\n⚙️ 检查Globals.py配置...")

    try:
        import Globals
        
        print(f"  📦 模型文件: {Globals.model_file}")
        print(f"  📊 数据集: {Globals.dataset}")
        print(f"  🔄 训练轮数: {Globals.epochs}")
        print(f"  📦 批次大小: {Globals.batch_size}")
        print(f"  🔥 启用蒸馏: {Globals.bool_distill}")
        print(f"  🌡️ 温度参数: {Globals.hyp_T}")
        print(f"  📈 Box蒸馏权重: {Globals.hyp_box_distill}")
        print(f"  🎯 Cls蒸馏权重: {Globals.hyp_cls_distill}")
        print(f"  📊 DFL蒸馏权重: {Globals.hyp_dfl_distill}")

        # 检查关键配置
        if not Globals.bool_distill:
            print("  ⚠️ 警告: 知识蒸馏未启用!")
            return False

        if "fruit" not in Globals.dataset:
            print("  ⚠️ 警告: 未使用fruit数据集!")
            return False

        if "fruit" not in Globals.model_file:
            print("  ⚠️ 警告: 未使用fruit模型!")
            return False
        
        print("  ✅ Globals.py配置正确")
        return True
        
    except Exception as e:
        print(f"  ❌ Globals.py配置错误: {e}")
        return False

def check_dataset():
    """检查数据集配置"""
    print("\n📊 检查数据集配置...")
    
    try:
        import Globals
        dataset = Globals.dataset
        
        if not os.path.exists(dataset):
            print(f"  ❌ 数据集文件不存在: {dataset}")
            return False
        
        # 读取数据集配置
        with open(dataset, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键配置
        if "nc: 3" in content:
            print("  ✅ 类别数量: 3 (apple, banana, orange)")
        else:
            print("  ⚠️ 警告: 类别数量不是3!")
        
        if "fruit" in content.lower():
            print("  ✅ 数据集路径包含fruit")
        else:
            print("  ⚠️ 警告: 数据集路径不包含fruit!")
        
        print("  ✅ 数据集配置正确")
        return True
        
    except Exception as e:
        print(f"  ❌ 数据集配置错误: {e}")
        return False

def check_model_config():
    """检查模型配置"""
    print("\n🏗️ 检查模型配置...")
    
    try:
        import Globals
        model_file = Globals.model_file
        
        if not os.path.exists(model_file):
            print(f"  ❌ 模型文件不存在: {model_file}")
            return False
        
        # 读取模型配置
        with open(model_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键配置
        if "nc: 3" in content:
            print("  ✅ 模型类别数量: 3")
        else:
            print("  ⚠️ 警告: 模型类别数量不是3!")
        
        print("  ✅ 模型配置正确")
        return True
        
    except Exception as e:
        print(f"  ❌ 模型配置错误: {e}")
        return False

def check_cuda():
    """检查CUDA环境"""
    print("\n🚀 检查CUDA环境...")
    
    if torch.cuda.is_available():
        print(f"  ✅ CUDA可用")
        print(f"  🎮 GPU: {torch.cuda.get_device_name(0)}")
        print(f"  💾 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB")
        
        # 检查GPU内存
        memory_gb = torch.cuda.get_device_properties(0).total_memory / 1e9
        if memory_gb < 4:
            print("  ⚠️ 警告: GPU内存可能不足，建议减小batch_size")
        
        return True
    else:
        print("  ⚠️ CUDA不可用，将使用CPU训练")
        return False

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    required_packages = [
        'torch',
        'torchvision', 
        'ultralytics',
        'numpy',
        'opencv-python',
        'pillow',
        'matplotlib',
        'pyyaml'
    ]
    
    all_installed = True
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - 未安装!")
            all_installed = False
    
    return all_installed

def generate_summary():
    """生成配置摘要"""
    print("\n📋 配置摘要:")
    print("=" * 50)
    
    try:
        import Globals
        
        print(f"🍎 项目: 知识蒸馏水果检测")
        print(f"📊 数据集: {Globals.dataset}")
        print(f"🏗️ 模型: {Globals.model_file}")
        print(f"🔄 训练轮数: {Globals.epochs}")
        print(f"📦 批次大小: {Globals.batch_size}")
        print(f"🌡️ 温度参数: {Globals.hyp_T}")
        print(f"🎯 预期MAP@0.5: > 97%")
        print(f"📈 预期MAP@0.5:0.95: > 92%")

        print("\n🚀 优化特性:")
        print(f"  📈 特征层统计: {Globals.use_feature_layer_stats}")
        print(f"  🌡️ 动态温度: {Globals.use_dynamic_temperature}")
        print(f"  ⛏️ 困难负样本挖掘: {Globals.use_hard_negative_mining}")
        print(f"  🔄 对比学习: {Globals.use_contrastive_learning}")
        
    except Exception as e:
        print(f"❌ 无法生成摘要: {e}")

def main():
    """主验证函数"""
    print("🔍 开始验证知识蒸馏配置")
    print("=" * 60)
    
    checks = [
        ("文件检查", check_files),
        ("Globals配置", check_globals_config),
        ("数据集配置", check_dataset),
        ("模型配置", check_model_config),
        ("CUDA环境", check_cuda),
        ("依赖包", check_dependencies)
    ]
    
    all_passed = True
    for name, check_func in checks:
        try:
            result = check_func()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ {name}检查失败: {e}")
            all_passed = False
    
    print("\n" + "=" * 60)
    
    if all_passed:
        print("🎉 所有检查通过! 配置正确!")
        generate_summary()
        print("\n🚀 可以开始训练:")
        print("   python train_enhanced_distillation.py")
    else:
        print("❌ 部分检查失败，请修复后重试!")
    
    return all_passed

if __name__ == "__main__":
    main()
