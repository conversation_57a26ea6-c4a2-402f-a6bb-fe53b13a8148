#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 修复知识蒸馏损失函数 - 移除不必要的样本计算，优化MAP性能
"""

import os
import sys
from pathlib import Path

def fix_loss_function():
    """修复损失函数中的问题"""
    
    loss_file = "ultralytics/utils/loss.py"
    
    # 读取原文件
    with open(loss_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复1: 简化蒸馏损失计算，移除不必要的变量
    fix1 = '''    def D_loss_calculate(self, distill_relative_teacher, distill_relative_student):
        """🔥 优化的知识蒸馏损失计算 - 专注MAP提升"""

        # [1]获取蒸馏相关参数
        [pred_scores_teacher, fg_mask_teacher, target_scores_teacher, batch_size_teacher,
         pred_distri_teacher, pred_bboxes_teacher, target_bboxes_teacher, target_ltrb_teacher] = distill_relative_teacher
        [pred_scores_student, fg_mask_student, target_scores_student, batch_size_student,
         pred_distri_student, pred_bboxes_student, target_bboxes_student, target_ltrb_student] = distill_relative_student

        # [2]统一的前景掩码 - 简化区域处理
        fg_mask_distill = fg_mask_student | fg_mask_teacher  # 合并前景区域
        self.batch_size = max(batch_size_teacher, batch_size_student)  # 使用较大的batch size

        # [3]🎯 优化分类蒸馏损失
        if fg_mask_distill.sum() == 0:
            distill_loss_cls = torch.tensor(0.0, device=self.device)
        else:
            # 正样本区域
            pred_scores_student_pos = pred_scores_student[fg_mask_distill]
            pred_scores_teacher_pos = pred_scores_teacher[fg_mask_distill]
            target_scores_student_pos = target_scores_student[fg_mask_distill]

            # 教师指导损失 - KL散度
            student_soft = F.log_softmax(pred_scores_student_pos / self.T, dim=-1)
            teacher_soft = F.softmax(pred_scores_teacher_pos / self.T, dim=-1)
            kl_loss = F.kl_div(student_soft, teacher_soft, reduction='batchmean') * (self.T ** 2)

            # Ground truth损失 - BCE
            gt_loss = F.binary_cross_entropy_with_logits(
                pred_scores_student_pos, target_scores_student_pos, reduction='mean'
            )

            # 加权组合
            distill_loss_cls = self.w_t_cls_1 * kl_loss + self.w_t_cls_2 * gt_loss

        # [4]🎯 优化边界框蒸馏损失
        if fg_mask_distill.sum() == 0:
            distill_loss_box = torch.tensor(0.0, device=self.device)
        else:
            # 正样本区域
            pred_bboxes_student_pos = pred_bboxes_student[fg_mask_distill]
            pred_bboxes_teacher_pos = pred_bboxes_teacher[fg_mask_distill]
            target_bboxes_student_pos = target_bboxes_student[fg_mask_distill]
            target_scores_student_pos = target_scores_student[fg_mask_distill]

            # 权重计算
            weight_box = target_scores_student_pos.sum(-1).unsqueeze(-1)

            # 教师指导损失 - CIoU
            from .metrics import bbox_iou
            iou_teacher = bbox_iou(pred_bboxes_student_pos, pred_bboxes_teacher_pos, xywh=False, CIoU=True)
            distill_loss_box_teacher = ((1.0 - iou_teacher) * weight_box).sum()

            # Ground truth损失 - CIoU
            iou_gt = bbox_iou(pred_bboxes_student_pos, target_bboxes_student_pos, xywh=False, CIoU=True)
            distill_loss_box_gt = ((1.0 - iou_gt) * weight_box).sum()

            # 加权组合
            distill_loss_box = self.w_t_box_1 * distill_loss_box_teacher + self.w_t_box_2 * distill_loss_box_gt

            # 归一化
            total_weight = weight_box.sum()
            if total_weight > 0:
                distill_loss_box = distill_loss_box / total_weight

        # [5]🎯 优化DFL蒸馏损失
        if fg_mask_distill.sum() == 0:
            distill_loss_dfl = torch.tensor(0.0, device=self.device)
        else:
            # 正样本区域
            pred_distri_student_pos = pred_distri_student[fg_mask_distill]
            pred_distri_teacher_pos = pred_distri_teacher[fg_mask_distill]
            target_ltrb_student_pos = target_ltrb_student[fg_mask_distill]
            target_scores_student_pos = target_scores_student[fg_mask_distill]

            # 权重计算
            weight_dfl = target_scores_student_pos.sum(-1).unsqueeze(-1)
            shape = pred_distri_student_pos.shape[:-1]

            # 教师指导损失
            pred_distri_student_flat = pred_distri_student_pos.view(-1, self.reg_max)
            target_dfl_teacher = F.softmax(pred_distri_teacher_pos.view(-1, self.reg_max) / self.T, dim=-1)
            
            student_log_prob = F.log_softmax(pred_distri_student_flat / self.T, dim=-1)
            distill_loss_dfl_teacher = F.kl_div(
                student_log_prob, target_dfl_teacher, reduction="none"
            ).sum(dim=-1).view(shape).mean(-1, keepdim=True) * (self.T ** 2)

            # Ground truth损失
            target_dfl_gt = torch.zeros(pred_distri_student_flat.shape, device=self.device)
            target_flat = target_ltrb_student_pos.view(-1, 1)

            # 处理边界情况
            target_flat = torch.clamp(target_flat, 0, self.reg_max - 1)
            tl = target_flat.long()
            tr = torch.clamp(tl + 1, 0, self.reg_max - 1)
            wl = target_flat - tl
            wr = 1 - wl

            # Label smoothing
            epsilon = 0.1
            target_dfl_gt.fill_(epsilon / self.reg_max)
            target_dfl_gt.scatter_(dim=-1, index=tl, src=wl * (1 - epsilon) + epsilon / self.reg_max)
            target_dfl_gt.scatter_(dim=-1, index=tr, src=wr * (1 - epsilon) + epsilon / self.reg_max)

            distill_loss_dfl_gt = F.cross_entropy(
                pred_distri_student_flat, target_dfl_gt, reduction="none"
            ).view(shape).mean(-1, keepdim=True)

            # 加权组合
            distill_loss_dfl_teacher_weighted = (distill_loss_dfl_teacher * weight_dfl).sum()
            distill_loss_dfl_gt_weighted = (distill_loss_dfl_gt * weight_dfl).sum()
            
            distill_loss_dfl = self.w_t_dfl_1 * distill_loss_dfl_teacher_weighted + self.w_t_dfl_2 * distill_loss_dfl_gt_weighted

            # 归一化
            total_weight = weight_dfl.sum()
            if total_weight > 0:
                distill_loss_dfl = distill_loss_dfl / total_weight

        return distill_loss_cls, distill_loss_box, distill_loss_dfl'''
    
    # 查找并替换D_loss_calculate函数
    import re
    
    # 找到函数开始位置
    pattern = r'def D_loss_calculate\(self, distill_relative_teacher, distill_relative_student\):.*?return distill_loss_cls, distill_loss_box, distill_loss_dfl'
    
    if re.search(pattern, content, re.DOTALL):
        content = re.sub(pattern, fix1, content, flags=re.DOTALL)
        print("✅ 已修复 D_loss_calculate 函数")
    else:
        print("⚠️ 未找到 D_loss_calculate 函数，手动添加")
    
    # 写回文件
    with open(loss_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("🔧 损失函数修复完成!")

if __name__ == "__main__":
    print("🔧 开始修复知识蒸馏损失函数...")
    fix_loss_function()
    print("✅ 修复完成!")
