#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 测试权重修复效果的脚本
验证COCO知识蒸馏配置是否正确
"""

import os
import torch
import Globals
from ultralytics import YOLO
from test_for_function import test_for_exchange_teacher

def test_weight_preparation():
    """测试权重准备"""
    print("🧪 测试权重准备...")
    
    # 检查原始教师模型
    if not os.path.exists(Globals.path_teacher):
        print(f"❌ 原始教师模型不存在: {Globals.path_teacher}")
        return False
    
    # 准备教师权重
    try:
        if not os.path.exists(Globals.path_teacher_prepare):
            print("🔄 准备教师权重...")
            test_for_exchange_teacher(Globals.path_teacher, Globals.path_teacher_prepare)
        
        # 验证权重文件 - 使用修复后的权重文件
        fixed_weight_path = "./(f)models/yolov8_teacher_prepare-coco_fixed.pth"
        if os.path.exists(fixed_weight_path):
            teacher_weights = torch.load(fixed_weight_path, map_location='cpu')
        else:
            teacher_weights = torch.load(Globals.path_teacher_prepare, map_location='cpu')
        print(f"✅ 教师权重文件包含 {len(teacher_weights)} 个参数")
        return True
        
    except Exception as e:
        print(f"❌ 权重准备失败: {e}")
        return False

def test_model_loading():
    """测试模型加载"""
    print("🧪 测试模型加载...")
    
    try:
        model = YOLO(Globals.model_file)
        param_count = sum(p.numel() for p in model.model.parameters())
        print(f"✅ 模型加载成功，参数数量: {param_count}")
        
        # 检查模型结构
        if hasattr(model.model, 'model') and len(model.model.model) > 0:
            last_layer = model.model.model[-1]
            print(f"📊 最后一层类型: {type(last_layer).__name__}")
            
            if hasattr(last_layer, 'nc'):
                print(f"📊 类别数: {last_layer.nc}")
                if last_layer.nc == 80:
                    print("✅ 类别数正确 (80类)")
                else:
                    print(f"⚠️ 类别数异常: {last_layer.nc}")
        
        return model
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None

def test_weight_loading(model):
    """测试权重加载"""
    print("🧪 测试权重加载...")
    
    try:
        # 加载教师权重
        teacher_weights = torch.load("./(f)models/yolov8_teacher_prepare-coco_fixed.pth", map_location='cpu')
        model_dict = model.model.state_dict()
        
        # 统计匹配情况
        matched = 0
        shape_mismatch = 0
        missing = 0
        
        for name, param in teacher_weights.items():
            if name in model_dict:
                if param.shape == model_dict[name].shape:
                    matched += 1
                else:
                    shape_mismatch += 1
            else:
                missing += 1
        
        print(f"📊 权重匹配统计:")
        print(f"  ✅ 匹配: {matched}")
        print(f"  ⚠️ 形状不匹配: {shape_mismatch}")
        print(f"  ❌ 缺失: {missing}")
        
        # 加载权重
        matched_weights = {k: v for k, v in teacher_weights.items() 
                          if k in model_dict and v.shape == model_dict[k].shape}
        
        model.model.load_state_dict(matched_weights, strict=False)
        print(f"✅ 权重加载完成: {len(matched_weights)} 个参数")
        
        return True
        
    except Exception as e:
        print(f"❌ 权重加载失败: {e}")
        return False

def test_teacher_freezing(model):
    """测试教师层冻结"""
    print("🧪 测试教师层冻结...")
    
    try:
        frozen_count = 0
        trainable_count = 0
        
        for name, param in model.model.named_parameters():
            parts = name.split('.')
            if len(parts) >= 3 and parts[2].isdigit():
                layer_num = int(parts[2])
                if layer_num in Globals.teacher_peer_list:
                    param.requires_grad = False
                    frozen_count += 1
                else:
                    trainable_count += 1
            else:
                trainable_count += 1
        
        print(f"🔒 教师层冻结统计:")
        print(f"  ❄️ 冻结参数: {frozen_count}")
        print(f"  🔥 可训练参数: {trainable_count}")
        
        if frozen_count > 0:
            print("✅ 教师层冻结成功")
            return True
        else:
            print("⚠️ 没有参数被冻结")
            return False
            
    except Exception as e:
        print(f"❌ 教师层冻结失败: {e}")
        return False

def test_forward_pass(model):
    """测试前向传播"""
    print("🧪 测试前向传播...")
    
    try:
        # 创建测试输入
        test_input = torch.randn(1, 3, 640, 640)
        
        # 测试前向传播
        model.model.eval()
        with torch.no_grad():
            output = model.model(test_input)
        
        print(f"✅ 前向传播成功")
        print(f"📊 输出数量: {len(output) if isinstance(output, (list, tuple)) else 1}")
        
        if isinstance(output, (list, tuple)):
            for i, o in enumerate(output):
                if hasattr(o, 'shape'):
                    print(f"  输出{i}: {o.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        return False

def test_dataset_config():
    """测试数据集配置"""
    print("🧪 测试数据集配置...")
    
    if not os.path.exists(Globals.dataset):
        print(f"❌ 数据集配置文件不存在: {Globals.dataset}")
        return False
    
    print(f"✅ 数据集配置文件存在: {Globals.dataset}")
    
    # 检查数据集路径
    try:
        import yaml
        with open(Globals.dataset, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        dataset_path = config.get('path', '')
        print(f"📂 数据集路径: {dataset_path}")
        
        if 'coco128' in dataset_path:
            print("✅ 使用coco128数据集，类别覆盖更全面")
        elif 'coco8' in dataset_path:
            print("⚠️ 使用coco8数据集，类别覆盖较少")
        
        nc = config.get('nc', 0)
        print(f"📊 类别数: {nc}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据集配置解析失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试权重修复效果")
    print("=" * 60)
    
    # 显示配置
    print(f"📁 模型文件: {Globals.model_file}")
    print(f"📊 数据集: {Globals.dataset}")
    print(f"🎯 知识蒸馏: {'启用' if Globals.bool_distill else '禁用'}")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: 权重准备
    result = test_weight_preparation()
    test_results.append(("权重准备", result))
    
    # 测试2: 模型加载
    model = test_model_loading()
    test_results.append(("模型加载", model is not None))
    
    if model is None:
        print("❌ 模型加载失败，无法继续测试")
        return False
    
    # 测试3: 权重加载
    result = test_weight_loading(model)
    test_results.append(("权重加载", result))
    
    # 测试4: 教师层冻结
    result = test_teacher_freezing(model)
    test_results.append(("教师层冻结", result))
    
    # 测试5: 前向传播
    result = test_forward_pass(model)
    test_results.append(("前向传播", result))
    
    # 测试6: 数据集配置
    result = test_dataset_config()
    test_results.append(("数据集配置", result))
    
    # 总结测试结果
    print("\n📊 测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} : {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"总体结果: {passed}/{len(test_results)} 项测试通过")
    
    if passed == len(test_results):
        print("🎉 所有测试通过！可以开始训练了")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🚀 运行 python start_coco_klite_training.py 开始训练")
    else:
        print("\n❌ 请先修复失败的测试项")
