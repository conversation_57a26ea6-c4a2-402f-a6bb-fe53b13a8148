#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 修复权重加载问题的脚本
解决COCO知识蒸馏中的权重恢复问题
"""

import os
import torch
import Globals
from ultralytics import YOLO
from test_for_function import test_for_exchange_teacher

def prepare_teacher_weights():
    """准备教师模型权重"""
    print("🔄 开始准备教师模型权重...")
    
    # 检查教师权重文件是否存在
    if os.path.exists(Globals.path_teacher_prepare):
        print(f"✅ 教师权重文件已存在: {Globals.path_teacher_prepare}")
        return True
    
    # 检查原始教师模型是否存在
    if not os.path.exists(Globals.path_teacher):
        print(f"❌ 原始教师模型不存在: {Globals.path_teacher}")
        return False
    
    try:
        # 转换教师权重
        test_for_exchange_teacher(Globals.path_teacher, Globals.path_teacher_prepare)
        print("✅ 教师权重准备完成")
        return True
    except Exception as e:
        print(f"❌ 教师权重准备失败: {e}")
        return False

def load_teacher_weights_safely(model):
    """安全加载教师权重到模型"""
    print("🔄 正在安全加载教师权重...")
    
    try:
        # 加载教师权重
        teacher_weights = torch.load(Globals.path_teacher_prepare, map_location='cpu')
        print(f"📊 教师权重文件包含 {len(teacher_weights)} 个参数")
        
        # 获取模型当前状态
        model_dict = model.model.state_dict()
        print(f"📊 目标模型包含 {len(model_dict)} 个参数")
        
        # 筛选匹配的权重
        matched_weights = {}
        teacher_loaded = 0
        teacher_skipped = 0
        
        for name, param in teacher_weights.items():
            if name in model_dict:
                if param.shape == model_dict[name].shape:
                    matched_weights[name] = param
                    teacher_loaded += 1
                else:
                    print(f"⚠️ 形状不匹配: {name} - 教师:{param.shape} vs 模型:{model_dict[name].shape}")
                    teacher_skipped += 1
            else:
                teacher_skipped += 1
        
        # 加载匹配的权重
        model.model.load_state_dict(matched_weights, strict=False)
        
        print(f"✅ 教师权重加载完成: {teacher_loaded} 个参数加载, {teacher_skipped} 个参数跳过")
        
        # 冻结教师层
        freeze_teacher_layers(model)
        
        return True
        
    except Exception as e:
        print(f"❌ 教师权重加载失败: {e}")
        return False

def freeze_teacher_layers(model):
    """冻结教师层参数"""
    print("🔒 正在冻结教师层...")
    
    frozen_count = 0
    total_params = 0
    
    for name, param in model.model.named_parameters():
        total_params += 1
        
        # 检查是否是教师层
        parts = name.split('.')
        if len(parts) >= 3 and parts[2].isdigit():
            layer_num = int(parts[2])
            if layer_num in Globals.teacher_peer_list:
                param.requires_grad = False
                frozen_count += 1
    
    print(f"🔒 教师层冻结完成: {frozen_count}/{total_params} 个参数被冻结")

def verify_model_structure(model):
    """验证模型结构"""
    print("🔍 验证模型结构...")
    
    try:
        # 检查模型是否有正确的检测头
        if hasattr(model.model, 'model') and len(model.model.model) > 0:
            last_layer = model.model.model[-1]
            print(f"📊 最后一层类型: {type(last_layer).__name__}")
            
            # 检查是否有知识蒸馏层
            if hasattr(last_layer, 'nc'):
                print(f"📊 类别数: {last_layer.nc}")
            
            return True
        else:
            print("❌ 模型结构异常")
            return False
            
    except Exception as e:
        print(f"❌ 模型结构验证失败: {e}")
        return False

def test_forward_pass(model):
    """测试前向传播"""
    print("🧪 测试模型前向传播...")
    
    try:
        # 创建测试输入
        test_input = torch.randn(1, 3, 640, 640)
        
        # 测试前向传播
        with torch.no_grad():
            output = model.model(test_input)
        
        print(f"✅ 前向传播成功，输出形状: {[o.shape if hasattr(o, 'shape') else type(o) for o in output]}")
        return True
        
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始修复权重加载问题")
    print("=" * 60)
    
    # 显示配置信息
    print(f"📁 模型文件: {Globals.model_file}")
    print(f"📊 数据集: {Globals.dataset}")
    print(f"🎯 知识蒸馏: {'启用' if Globals.bool_distill else '禁用'}")
    print(f"📂 教师权重: {Globals.path_teacher_prepare}")
    print("=" * 60)
    
    # 步骤1: 准备教师权重
    if not prepare_teacher_weights():
        print("❌ 教师权重准备失败，退出")
        return False
    
    # 步骤2: 加载模型
    print("\n🔧 加载知识蒸馏模型...")
    try:
        model = YOLO(Globals.model_file)
        print(f"✅ 模型加载成功，参数数量: {sum(p.numel() for p in model.model.parameters())}")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False
    
    # 步骤3: 验证模型结构
    if not verify_model_structure(model):
        print("❌ 模型结构验证失败，退出")
        return False
    
    # 步骤4: 加载教师权重
    if not load_teacher_weights_safely(model):
        print("❌ 教师权重加载失败，退出")
        return False
    
    # 步骤5: 测试前向传播
    if not test_forward_pass(model):
        print("❌ 前向传播测试失败，退出")
        return False
    
    # 步骤6: 保存修复后的模型
    try:
        fixed_model_path = "./(f)models/yolov8_klite_coco_fixed.pt"
        torch.save(model.model.state_dict(), fixed_model_path)
        print(f"💾 修复后的模型已保存: {fixed_model_path}")
    except Exception as e:
        print(f"⚠️ 模型保存失败: {e}")
    
    print("\n🎉 权重加载问题修复完成!")
    print("✅ 现在可以开始训练了")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 权重修复失败，请检查错误信息")
        exit(1)
    else:
        print("\n🚀 可以运行 python start_coco_klite_training.py 开始训练")
