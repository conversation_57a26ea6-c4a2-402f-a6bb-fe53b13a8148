#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import torch
import Globals
from ultralytics import YOLO

# 加载权重和模型
weight_file = "./(f)models/yolov8_teacher_prepare-coco_fixed.pth"
print(f"加载教师权重文件: {weight_file}")
teacher_weights = torch.load(weight_file, map_location='cpu')
print(f"教师权重前5个键: {list(teacher_weights.keys())[:5]}")

model = YOLO(Globals.model_file)
model_dict = model.model.state_dict()

print(f"教师权重数量: {len(teacher_weights)}")
print(f"模型参数数量: {len(model_dict)}")

# 测试几个参数
test_names = ['model.1.conv.weight', 'model.2.conv.weight', 'model.3.conv.weight']

for test_name in test_names:
    print(f"\n测试参数: {test_name}")
    print(f"在教师权重中: {test_name in teacher_weights}")
    print(f"在模型中: {test_name in model_dict}")
    
    if test_name in teacher_weights and test_name in model_dict:
        t_shape = teacher_weights[test_name].shape
        m_shape = model_dict[test_name].shape
        print(f"教师形状: {t_shape}")
        print(f"模型形状: {m_shape}")
        print(f"形状匹配: {t_shape == m_shape}")

# 统计匹配情况
matched = 0
shape_mismatch = 0
missing = 0

for name, param in teacher_weights.items():
    if name in model_dict:
        if param.shape == model_dict[name].shape:
            matched += 1
        else:
            shape_mismatch += 1
    else:
        missing += 1

print(f"\n权重匹配统计:")
print(f"匹配: {matched}")
print(f"形状不匹配: {shape_mismatch}")
print(f"缺失: {missing}")
