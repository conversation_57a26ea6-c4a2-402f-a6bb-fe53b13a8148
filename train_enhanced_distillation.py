#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🍎 增强知识蒸馏训练脚本 - 优化MAP性能
基于用户记忆的最佳实践，回到高质量fruit数据集
"""

import os
import sys
import torch
import warnings
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 导入配置
from Globals import *
from ultralytics import YOLO
from ultralytics.utils import LOGGER

# 忽略警告
warnings.filterwarnings('ignore')

def setup_environment():
    """设置训练环境"""
    # 设置CUDA
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        LOGGER.info(f"🚀 使用GPU: {torch.cuda.get_device_name(0)}")
        LOGGER.info(f"💾 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB")
    else:
        LOGGER.warning("⚠️ 未检测到CUDA，使用CPU训练")
    
    # 创建模型目录
    os.makedirs("(f)models", exist_ok=True)
    
    return True

def prepare_models():
    """准备教师和学生模型"""
    LOGGER.info("🔧 准备知识蒸馏模型...")
    
    # 检查模型文件
    if not os.path.exists(model_file):
        LOGGER.error(f"❌ 模型文件不存在: {model_file}")
        return False
    
    if not os.path.exists(dataset):
        LOGGER.error(f"❌ 数据集文件不存在: {dataset}")
        return False
    
    LOGGER.info(f"✅ 模型配置: {model_file}")
    LOGGER.info(f"✅ 数据集配置: {dataset}")
    LOGGER.info(f"🎯 训练轮数: {epochs}")
    LOGGER.info(f"📦 批次大小: {batch_size}")
    
    return True

def print_distillation_config():
    """打印知识蒸馏配置"""
    LOGGER.info("🔥 知识蒸馏配置:")
    LOGGER.info(f"  🌡️  温度参数: {hyp_T}")
    LOGGER.info(f"  📦 Box蒸馏权重: {hyp_box_distill}")
    LOGGER.info(f"  🎯 Cls蒸馏权重: {hyp_cls_distill}")
    LOGGER.info(f"  📊 DFL蒸馏权重: {hyp_dfl_distill}")
    LOGGER.info(f"  👨‍🏫 教师权重 - Cls: {hyp_w_t_cls}")
    LOGGER.info(f"  👨‍🏫 教师权重 - Box: {hyp_w_t_box}")
    LOGGER.info(f"  👨‍🏫 教师权重 - DFL: {hyp_w_t_dfl}")
    LOGGER.info(f"  🎨 标签平滑: {label_smoothing}")
    
    LOGGER.info("🚀 高级特性:")
    LOGGER.info(f"  📈 特征层统计: {use_feature_layer_stats}")
    LOGGER.info(f"  🌡️  动态温度: {use_dynamic_temperature}")
    LOGGER.info(f"  ⛏️  困难负样本挖掘: {use_hard_negative_mining}")
    LOGGER.info(f"  🔄 对比学习: {use_contrastive_learning}")

def train_enhanced_distillation():
    """执行增强知识蒸馏训练"""
    try:
        # 设置环境
        if not setup_environment():
            return False
        
        # 准备模型
        if not prepare_models():
            return False
        
        # 打印配置
        print_distillation_config()
        
        # 创建YOLO模型
        LOGGER.info("🏗️ 创建YOLO模型...")
        model = YOLO(model_file)
        
        # 训练参数
        train_args = {
            'data': dataset,
            'epochs': epochs,
            'batch': batch_size,
            'device': device,
            'save': bool_save,
            'save_period': save_period,
            'resume': bool_resume,
            'project': 'runs/detect',
            'name': f'enhanced_fruit_distill_T{hyp_T}_ep{epochs}',
            'exist_ok': True,
            'pretrained': True,
            'optimizer': 'AdamW',  # 使用AdamW优化器
            'lr0': 0.001,  # 初始学习率
            'lrf': 0.01,   # 最终学习率因子
            'momentum': 0.937,
            'weight_decay': 0.0005,
            'warmup_epochs': 3.0,
            'warmup_momentum': 0.8,
            'warmup_bias_lr': 0.1,
            'box': 7.5,    # box损失增益
            'cls': 0.5,    # cls损失增益
            'dfl': 1.5,    # dfl损失增益
            'label_smoothing': label_smoothing,
            'val': True,
            'plots': True,
            'verbose': True,
            'seed': 42,    # 固定随机种子
            'deterministic': True,
            'workers': 8,
            'amp': True,   # 自动混合精度
            'fraction': 1.0,
            'profile': False,
            'freeze': teacher_peer_list,  # 冻结教师层
            'patience': 50,  # 早停耐心
            'close_mosaic': 10,  # 关闭马赛克增强的轮数
        }
        
        LOGGER.info("🚀 开始增强知识蒸馏训练...")
        LOGGER.info(f"📊 预期提升: MAP@0.5 > 97%, MAP@0.5:0.95 > 92%")
        
        # 开始训练
        results = model.train(**train_args)
        
        # 训练完成
        LOGGER.info("✅ 训练完成!")
        LOGGER.info(f"📈 最终结果: {results}")
        
        # 保存最终模型
        if hasattr(model, 'model'):
            torch.save(model.model.state_dict(), final)
            LOGGER.info(f"💾 模型已保存: {final}")
        
        return True
        
    except Exception as e:
        LOGGER.error(f"❌ 训练失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def validate_model():
    """验证训练后的模型"""
    try:
        LOGGER.info("🔍 验证训练后的模型...")
        
        # 加载训练好的模型
        model = YOLO(final)
        
        # 验证
        results = model.val(data=dataset, device=device)
        
        LOGGER.info("📊 验证结果:")
        LOGGER.info(f"  🎯 mAP@0.5: {results.box.map50:.4f}")
        LOGGER.info(f"  📈 mAP@0.5:0.95: {results.box.map:.4f}")
        LOGGER.info(f"  🎯 Precision: {results.box.mp:.4f}")
        LOGGER.info(f"  📊 Recall: {results.box.mr:.4f}")
        
        return True
        
    except Exception as e:
        LOGGER.error(f"❌ 验证失败: {str(e)}")
        return False

if __name__ == "__main__":
    LOGGER.info("🍎 启动增强知识蒸馏训练")
    LOGGER.info("=" * 60)
    
    # 执行训练
    success = train_enhanced_distillation()
    
    if success:
        LOGGER.info("🎉 训练成功完成!")
        
        # 验证模型
        validate_model()
        
        LOGGER.info("🏆 增强知识蒸馏训练全部完成!")
    else:
        LOGGER.error("💥 训练失败!")
        sys.exit(1)
