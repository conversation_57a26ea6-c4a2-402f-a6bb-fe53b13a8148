#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 修复教师权重映射的脚本
创建正确的教师-学生层映射关系
"""

import torch
import Globals
from ultralytics import YOLO

def analyze_model_layers():
    """分析教师和学生模型的层结构"""
    print("🔍 分析模型层结构...")
    
    # 加载教师模型
    teacher_model = YOLO(Globals.path_teacher)
    teacher_layers = set()
    teacher_params = {}
    
    for name in teacher_model.model.state_dict().keys():
        parts = name.split('.')
        if len(parts) >= 2 and parts[1].isdigit():
            layer_num = int(parts[1])
            teacher_layers.add(layer_num)
            if layer_num not in teacher_params:
                teacher_params[layer_num] = []
            teacher_params[layer_num].append(name)
    
    # 加载学生模型
    student_model = YOLO(Globals.model_file)
    student_layers = set()
    student_params = {}
    
    for name in student_model.model.state_dict().keys():
        parts = name.split('.')
        if len(parts) >= 2 and parts[1].isdigit():
            layer_num = int(parts[1])
            student_layers.add(layer_num)
            if layer_num not in student_params:
                student_params[layer_num] = []
            student_params[layer_num].append(name)
    
    print(f"📊 教师模型层数: {sorted(teacher_layers)}")
    print(f"📊 学生模型层数: {sorted(student_layers)}")
    
    return teacher_layers, student_layers, teacher_params, student_params

def create_layer_mapping(teacher_layers, student_layers):
    """创建教师-学生层映射"""
    print("🔗 创建层映射关系...")
    
    teacher_sorted = sorted(teacher_layers)
    student_sorted = sorted(student_layers)
    
    # 找到学生模型中的教师层（前半部分）
    teacher_target_layers = student_sorted[:len(teacher_sorted)]
    
    mapping = {}
    for i, teacher_layer in enumerate(teacher_sorted):
        if i < len(teacher_target_layers):
            mapping[teacher_layer] = teacher_target_layers[i]
    
    print(f"📋 层映射关系: {mapping}")
    return mapping

def convert_teacher_weights_fixed():
    """修复版本的教师权重转换"""
    print("🔧 开始修复教师权重转换...")
    
    # 分析层结构
    teacher_layers, student_layers, teacher_params, student_params = analyze_model_layers()
    
    # 创建映射
    layer_mapping = create_layer_mapping(teacher_layers, student_layers)
    
    # 加载教师模型权重
    teacher_model = YOLO(Globals.path_teacher)
    teacher_state_dict = teacher_model.model.state_dict()
    
    save_state = {}
    converted_count = 0
    skipped_count = 0
    
    for param_name, param_value in teacher_state_dict.items():
        try:
            # 首先处理前缀问题
            clean_name = param_name
            if param_name.startswith('model.model.'):
                # 去掉多余的model.前缀
                clean_name = param_name.replace('model.model.', 'model.', 1)

            parts = clean_name.split('.')

            # 检查是否是层参数
            if len(parts) >= 3 and parts[1] == 'model' and parts[2].isdigit():
                # 格式: model.model.X.xxx -> model.Y.xxx
                old_layer = int(parts[2])

                # 查找映射
                if old_layer in layer_mapping:
                    new_layer = layer_mapping[old_layer]
                    new_param_name = clean_name.replace(f"model.{old_layer}.", f"model.{new_layer}.", 1)
                    save_state[new_param_name] = param_value
                    converted_count += 1
                else:
                    print(f"⚠️ 层 {old_layer} 没有映射目标，跳过: {param_name}")
                    skipped_count += 1
            elif len(parts) >= 2 and parts[1].isdigit():
                # 格式: model.X.xxx -> model.Y.xxx
                old_layer = int(parts[1])

                # 查找映射
                if old_layer in layer_mapping:
                    new_layer = layer_mapping[old_layer]
                    new_param_name = clean_name.replace(f"model.{old_layer}.", f"model.{new_layer}.", 1)
                    save_state[new_param_name] = param_value
                    converted_count += 1
                else:
                    print(f"⚠️ 层 {old_layer} 没有映射目标，跳过: {param_name}")
                    skipped_count += 1
            else:
                # 非层参数，直接复制（使用清理后的名称）
                save_state[clean_name] = param_value
                converted_count += 1
                
        except Exception as e:
            print(f"❌ 处理参数 {param_name} 时出错: {e}")
            skipped_count += 1
    
    print(f"✅ 权重转换完成: {converted_count} 个参数转换, {skipped_count} 个参数跳过")
    print(f"📊 转换后参数数量: {len(save_state)}")
    
    # 保存权重
    output_path = "./(f)models/yolov8_teacher_prepare-coco_fixed.pth"
    torch.save(save_state, output_path)
    print(f"💾 修复后的教师权重已保存到: {output_path}")
    
    return output_path, layer_mapping

def test_weight_matching(weight_path):
    """测试权重匹配情况"""
    print("🧪 测试权重匹配...")
    
    # 加载权重
    teacher_weights = torch.load(weight_path, map_location='cpu')
    student_model = YOLO(Globals.model_file)
    student_dict = student_model.model.state_dict()
    
    matched = 0
    shape_mismatch = 0
    missing = 0
    
    for name, param in teacher_weights.items():
        if name in student_dict:
            if param.shape == student_dict[name].shape:
                matched += 1
            else:
                shape_mismatch += 1
                print(f"⚠️ 形状不匹配: {name} - 教师:{param.shape} vs 学生:{student_dict[name].shape}")
        else:
            missing += 1
    
    print(f"📊 权重匹配统计:")
    print(f"  ✅ 匹配: {matched}")
    print(f"  ⚠️ 形状不匹配: {shape_mismatch}")
    print(f"  ❌ 缺失: {missing}")
    
    return matched > 0

def main():
    """主函数"""
    print("🚀 开始修复教师权重映射")
    print("=" * 60)
    
    try:
        # 转换权重
        weight_path, layer_mapping = convert_teacher_weights_fixed()
        
        # 测试匹配
        if test_weight_matching(weight_path):
            print("✅ 权重修复成功！")
            
            # 更新Globals.py中的路径
            print("🔄 更新配置文件...")
            with open('Globals.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换路径
            old_path = 'path_teacher_prepare = "./(f)models/yolov8_teacher_prepare-coco.pth_fixed"'
            new_path = 'path_teacher_prepare = "./(f)models/yolov8_teacher_prepare-coco_fixed.pth"'
            
            if old_path in content:
                content = content.replace(old_path, new_path)
                with open('Globals.py', 'w', encoding='utf-8') as f:
                    f.write(content)
                print("✅ 配置文件已更新")
            
            return True
        else:
            print("❌ 权重修复失败")
            return False
            
    except Exception as e:
        print(f"❌ 权重修复过程出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 权重修复完成！现在可以运行训练了")
        print("🚀 运行: python test_weight_fix.py 验证修复效果")
    else:
        print("\n❌ 权重修复失败，请检查错误信息")
