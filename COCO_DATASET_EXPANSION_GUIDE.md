# 📈 COCO数据集扩展指南

## 🎯 扩展策略

基于成功的fruit数据集配置，我们采用渐进式扩展策略：

### 阶段1: 验证配置 (当前)
- **数据集**: coco8 (4训练 + 4验证)
- **目的**: 验证知识蒸馏配置正确性
- **预期**: 训练能正常运行，无错误

### 阶段2: 小规模测试
- **数据集**: coco128 (128张图片)
- **目的**: 验证在稍大数据集上的性能
- **修改方法**: 
  ```yaml
  # 修改 coco-subset.yaml
  path: ../datasets/coco128
  train: images/train2017
  val: images/train2017
  ```

### 阶段3: 中等规模
- **数据集**: coco_10percent (~11,800张训练图片)
- **目的**: 获得有意义的mAP指标
- **修改方法**: 
  ```python
  # 修改 Globals.py
  dataset = "ultralytics/cfg/datasets/coco_10percent_fixed.yaml"
  ```

### 阶段4: 完整数据集
- **数据集**: 完整COCO (~118,000张训练图片)
- **目的**: 最终性能评估
- **修改方法**: 
  ```python
  # 修改 Globals.py
  dataset = "ultralytics/cfg/datasets/coco.yaml"
  ```

## 🔧 配置文件对比

### 当前配置 (coco8)
```yaml
path: ../datasets/coco8
train: images/train  # 4 images
val: images/val      # 4 images
```

### coco128配置
```yaml
path: ../datasets/coco128
train: images/train2017  # 128 images
val: images/train2017    # 128 images
```

### coco_10percent配置
```yaml
path: D:/catalogue/catalogue-coco_datasets/coco
train: train2017_10percent.txt  # ~11,800 images
val: val2017_10percent.txt      # ~500 images
```

### 完整COCO配置
```yaml
path: D:/catalogue/catalogue-coco_datasets/coco
train: train2017.txt  # 118,287 images
val: val2017.txt      # 5,000 images
```

## 📊 性能预期

### coco8 (验证阶段)
- **目标**: 训练无错误，损失下降
- **mAP**: 不重要，主要验证配置

### coco128 (小规模测试)
- **目标**: mAP > 0.1
- **训练时间**: ~10分钟

### coco_10percent (中等规模)
- **目标**: mAP@0.5 > 0.3
- **训练时间**: ~2-4小时
- **GPU内存**: 需要6GB+

### 完整COCO (最终目标)
- **目标**: mAP@0.5 > 0.4
- **训练时间**: ~12-24小时
- **GPU内存**: 需要8GB+

## 🚀 快速切换方法

### 方法1: 修改Globals.py
```python
# 切换到coco128
dataset = "ultralytics/cfg/datasets/coco128.yaml"

# 切换到10%子集
dataset = "ultralytics/cfg/datasets/coco_10percent_fixed.yaml"

# 切换到完整COCO
dataset = "ultralytics/cfg/datasets/coco.yaml"
```

### 方法2: 创建专用配置文件
```bash
# 复制当前配置
cp coco-subset.yaml coco-subset-128.yaml
cp coco-subset.yaml coco-subset-10percent.yaml
cp coco-subset.yaml coco-subset-full.yaml

# 分别修改路径和文件名
```

### 方法3: 命令行参数
```python
# 在训练脚本中添加参数支持
import argparse
parser.add_argument('--dataset', default='coco-subset.yaml')
```

## ⚠️ 注意事项

### 内存管理
- **coco8/coco128**: batch_size=16 正常
- **coco_10percent**: 可能需要 batch_size=8
- **完整COCO**: 可能需要 batch_size=4

### 训练时间
- **coco8**: 几分钟
- **coco128**: ~10分钟
- **coco_10percent**: 2-4小时
- **完整COCO**: 12-24小时

### 存储空间
- **coco8**: ~1MB
- **coco128**: ~7MB
- **coco_10percent**: ~2GB
- **完整COCO**: ~20GB

## 📝 推荐流程

1. **首先运行coco8验证**:
   ```bash
   python start_coco_klite_training.py
   ```

2. **确认无错误后切换到coco128**:
   ```python
   # 修改 Globals.py
   dataset = "ultralytics/cfg/datasets/coco128.yaml"
   ```

3. **获得合理mAP后扩展到10%**:
   ```python
   # 修改 Globals.py  
   dataset = "ultralytics/cfg/datasets/coco_10percent_fixed.yaml"
   epochs = 200  # 增加训练轮数
   ```

4. **最终扩展到完整COCO**:
   ```python
   # 修改 Globals.py
   dataset = "ultralytics/cfg/datasets/coco.yaml"
   epochs = 300  # 进一步增加训练轮数
   batch_size = 8  # 可能需要减少batch size
   ```

## 🎯 成功标准

- **coco8**: 训练完成无错误
- **coco128**: mAP@0.5 > 0.1
- **coco_10percent**: mAP@0.5 > 0.3
- **完整COCO**: mAP@0.5 > 0.4

达到这些标准后，说明知识蒸馏配置在COCO数据集上工作正常！
