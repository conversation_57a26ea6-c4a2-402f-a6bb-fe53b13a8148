from ultralytics import YOLO
from Globals import *
import torch


def test_for_metrix2function():
    # 矩阵函数化：
    # 加载模型
    # model_teacher = YOLO("runs/detect/train21/weights/best.pt")  # 加载预训练模型（建议用于训练）
    model = YOLO("yolov8-lite.yaml")  # 从头开始构建新模型

    # model_student.model.model._modules.append(model_teacher.model.model._modules)
    # print(model_student.model.model._modules)
    # kernel = model.state_dict()
    # test_function.test_Functional(kernel)
    # 训练模型
    model.train(data="coco128.yaml", epochs=5, save=True, save_period=2,
                freeze=[23, 24, 25, 26, 27, 28, 29, 30, 31, 33],
                resume=True, device='0')
    # model.train(data="coco128.yaml", epochs=3, save=True, save_period=3, freeze=[23, 24, 25, 26, 27, 28, 29, 30, 31, 33],
    # resume=True, device='0')
    # 在验证集上评估模型性能
    # model = YOLO("runs/detect/train15/weights/best.pt")
    metrics = model.val()
    # 打印所有层的参数

    # for name in model.state_dict():
    #     print(name)
    # 打印某层的参数
    # print(model.state_dict()['model.model.0.conv.weight'])
    # 对图像进行预测
    # results = model("https://ultralytics.com/images/bus.jpg")
    # 将模型导出为 ONNX 格式
    # success = model.export(format="onnx")

    model = YOLO("yolov8n.pt")  # 加载预训练模型（建议用于训练）
    kernel = model.state_dict()['model.model.0.conv.weight']
    print(kernel)
    # [C, H, W] = test_function.test_Functional(kernel)


def test_for_backward():
    x_data = torch.Tensor([[1.0], [2.0], [3.0]])
    y_data = torch.Tensor([[2.0], [4.0], [6.0]])

    class LinearModel(torch.nn.Module):
        def __init__(self):
            super(LinearModel, self).__init__()
            self.linear = torch.nn.Linear(1, 1)

        def forward(self, x):
            y_pred = self.linear(x)
            return y_pred

    model = LinearModel()
    criterion = torch.nn.MSELoss(size_average=False)
    optimizer = torch.optim.SGD(model.parameters(), lr=0.01)

    for epoch in range(1000):
        y_pred = model(x_data)
        loss = criterion(y_pred, y_data)
        # print(epoch, loss.item())

        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

    print('w = ', model.linear.weight.item())
    print('b = ', model.linear.bias.item())
    x_test = torch.Tensor([[4.0]])
    y_test = model(x_test)
    print('y_pred = ', y_test.data)


def test_for_bce():
    # 证明交叉熵公式满足：对于任意的自变量i(令此时两函数取值分别为x(i)与y(i)，记为x与y)，x与y越接近，交叉熵的值越小
    from sympy import symbols
    from sympy import E as e
    from sympy import log
    from sympy import diff
    from sympy.plotting import plot3d

    # 注意：由于sympy库的限制，某一自变量的变化范围，不能受其它自变量影响。因此只能做到以下程度：
    x, y, a = symbols('x y a')
    b = e
    # 绘制交叉熵函数的三维曲线
    z1 = x * log(x) + (1 - x) * log(1 - x) - x * log(y) - (1 - x) * log(1 - y)
    plot3d((z1, (x, 0, 1), (y, 0, 1)))
    z2 = y * log(y) + (1 - y) * log(1 - y)
    plot3d((z2, (x, 0, 1), (y, 0, 1)))
    print("可以看到二元交叉熵的值在y=x时达到最小值，并且这个最小值在y取0.5时将也取得最小值-ln(2)=-0.7(近似) ，验证完成")


def test_for_ce():
    import torch
    import torch.nn as nn
    from sympy import log

    # 假设有五个分类
    # 真实值设置为label
    label = torch.Tensor([0, 0, 0, 0, 0.56])
    # 预测值设置为pred
    pred = torch.Tensor([-0.2, 3, -5, 6, 9])

    pred_sig = torch.sigmoid(pred)
    pred_sf = torch.softmax(pred, -1)

    # 测试BCELoss过程
    loss_1 = nn.BCELoss(reduction='none')
    loss_2 = []
    # log默认底数为e
    for i in range(0, len(pred)):
        loss_2.append(label[i] * log(pred_sig[i]) + (1 - label[i]) * log(1 - pred_sig[i]))
    loss_2 = torch.FloatTensor(loss_2)
    loss_2 = - loss_2
    print("实际BCELoss结果为：", loss_1(pred_sig, label))
    print("测试BCELoss结果为：", loss_2)

    # 测试BCEWithLogitsLoss过程
    loss_3 = nn.BCEWithLogitsLoss(reduction='none')
    loss_4 = loss_2
    print("实际BCEWithLogitsLoss结果为：", loss_3(pred, label))
    print("测试BCEWithLogitsLoss结果为：", loss_4)

    # 测试cross_entropy过程
    loss_5 = nn.functional.cross_entropy(pred, label, reduction='none')
    loss_6 = []
    for i in range(0, len(pred)):
        loss_6.append(label[i] * log(pred_sf[i]))
    loss_6 = torch.FloatTensor(loss_6).sum()
    loss_6 = - loss_6
    print("实际cross_entropy结果为：", loss_5)
    print("测试cross_entropy结果为：", loss_6)

    # 测试cross_entropy在输入相差一维时的运算过程
    label = torch.Tensor([0, 0, 0, 0, 1]).long()  # 一个长度为N的向量，表示这N个参数各自的类别（一共C个类别，只能取到是0到C的整数倍）
    pred = torch.Tensor([[0.1, 0.1, 0.1, 0.1],
                         [1, 2, 3, 4],
                         [0.1, 0.1, -0.1, -0.1],
                         [0.1, 0.2, -0.3, -0.4],
                         [-0.1, -0.2, -0.3, -0.4]])  # 一个格式为(N, C)的矩阵， 表示这N个参数分别属于这C个类别的可能性
    pred_sf = torch.softmax(pred, -1)
    loss_7 = nn.functional.cross_entropy(pred, label, reduction='none')
    loss_8 = []
    for i in range(0, len(pred_sf)):
        loss_8.append(log(pred_sf[i][label[i]]))
    loss_8 = torch.FloatTensor(loss_8)
    loss_8 = - loss_8
    print("实际cross_entropy结果为：", loss_7)
    print("测试cross_entropy结果为：", loss_8)

    # 总结：
    print("\n总结：")
    print("BCELoss过程实质为：\n 1.求解 y*log(x) + (1-y)*log(1-x) \n 2.对结果取负值")
    print("BCEWithLogitsLoss过程实质为：\n 1.对输入x求sigmoid \n 2.求解 y*log(x) + (1-y)*log(1-x) \n 3.对结果取负值")
    print("cross_entropy过程实质为：\n 1.对输入x求softmax \n 2.求解 y*log(x)并求和 \n 3.对结果取负值")
    print("cross_entropy在输入相差一维时运算过程实质为：\n 1.对输入x求softmax \n 2.求解 log(x(y)) \n 3.对结果取负值")


def test_for_tensor_index():
    import torch

    # 测试tensor索引tensor的运算过程：
    x = torch.LongTensor(
        [[[1, 2, 3, 4], [5, 6, 7, 8], [9, 10, 11, 12]], [[13, 14, 15, 16], [17, 18, 19, 20], [21, 22, 23, 24]]])
    print(x.shape)
    y = torch.LongTensor([[1, 1, 0, 1, 0, 1], [1, 1, 0, 1, 0, 1]])
    print(y.shape)
    print("x[y, :, y].shape[0]和shape[1]应当为：", y.shape[0], y.shape[1])
    print("x[y, :, y].shape[2]应当为：", x.shape[1])
    print("x[y, :, y].shape实际为：", x[y, :, y].shape)


def test_for_tensor_multiply_C():
    x = torch.Tensor(
        [[[1, 2, 3, 4], [5, 6, 7, 8], [9, 10, 11, 12]], [[13, 14, 15, 16], [17, 18, 19, 20], [21, 22, 23, 24]]])
    print(x / 10)


def test_for_cls_and_iou_and_dlf():
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    from ultralytics.utils.metrics import bbox_iou
    from ultralytics.utils.tal import bbox2dist

    bce = nn.BCEWithLogitsLoss(reduction="none")

    def bce_WithoutLogits(x, y):
        return -(y * torch.log(x) + (1 - y) * torch.log(1 - x))

    def sigmoid_T(x, T):
        return 1 / (1 + T * torch.exp(-x))

    # 设置初始测试数据
    x = [-11., -6., -2., 2.]
    z = [0., 0., 0., 1.]

    x = torch.tensor(x, requires_grad=True)
    z = torch.tensor(z, requires_grad=True)
    T = 10000

    x_1 = sigmoid_T(x, T)
    z_1 = sigmoid_T(z, T)

    loss_bce = (pow(T+1, 2)/T) * bce_WithoutLogits(x_1, z_1).sum()
    loss_bce.backward()

    print(x.grad)

    print("综上，对BCE过程的修正值为T，对CE过程的修正值为T^2")

    # pred_distri = [[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6],
    #                [0.1, 0.2, 0.3, 2.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6],
    #                [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 5.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6],
    #                [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 7.6]]
    # pred_distri = torch.tensor(pred_distri, requires_grad=True)
    # pred_med = torch.softmax(pred_distri / T, dim=-1).matmul(
    #     torch.arange(16, dtype=torch.float).type(pred_distri.dtype))
    # pred_bboxes = pred_med * torch.tensor([-1, -1, 1, 1]) + 20
    # print(pred_bboxes)
    #
    # pred_distri_target = \
    #     [[0.12, 0.21, 0.33, 0.42, 0.53, 0.64, 0.75, 0.83, 0.95, 1.04, 1.12, 1.23, 1.31, 1.44, 1.52, 1.63],
    #      [0.12, 0.21, 0.33, 0.42, 0.53, 0.64, 0.75, 0.83, 0.95, 1.04, 1.12, 1.23, 1.31, 1.44, 1.52, 1.63],
    #      [0.12, 0.21, 0.33, 0.42, 0.53, 0.64, 0.75, 0.83, 0.95, 1.04, 1.12, 1.23, 1.31, 1.44, 1.52, 1.63],
    #      [0.12, 0.21, 0.33, 0.42, 0.53, 0.64, 0.75, 0.83, 0.95, 1.04, 1.12, 1.23, 1.31, 1.44, 1.52, 1.63]]
    # pred_distri_target = torch.tensor(pred_distri_target, requires_grad=True)
    # pred_med_target = torch.softmax(pred_distri_target / T, dim=-1).matmul(
    #     torch.arange(16, dtype=torch.float).type(pred_distri_target.dtype))
    # pred_bboxes_target = pred_med_target * torch.tensor([-1, -1, 1, 1]) + 20
    # print(pred_bboxes_target)
    #
    # anchor_points = torch.tensor([20, 20])
    # weight = 1
    # iou = bbox_iou(pred_bboxes, pred_bboxes_target, xywh=False, CIoU=True)
    # loss_iou = T * ((1.0 - iou) * weight).sum()
    # print(loss_iou)
    #
    # # loss_iou.backward()
    # # print(pred_distri.grad)
    # print("综上，对bbox_iou过程的修正值为T")
    #
    # # DFL loss
    # def df_loss(pred_dist, target):
    #     """Return sum of left and right DFL losses."""
    #     # Distribution Focal Loss (DFL) proposed in Generalized Focal Loss https://ieeexplore.ieee.org/document/9792391
    #     tl = target.long()  # target left
    #     tr = tl + 1  # target right
    #     wl = tr - target  # weight left
    #     wr = 1 - wl  # weight right
    #     return (F.cross_entropy(pred_dist, tl.view(-1), reduction='none').view(tl.shape) * wl +
    #             F.cross_entropy(pred_dist, tr.view(-1), reduction='none').view(tl.shape) * wr).mean(-1,
    #                                                                                                 keepdim=True)
    #
    # target_ltrb = bbox2dist(anchor_points, pred_bboxes_target, 16)
    # loss_dfl = df_loss(pred_distri,
    #                    target_ltrb) * weight
    # loss_dfl = loss_dfl.sum()
    # loss_dfl.backward()
    # print(pred_distri.grad)
    # print("综上，对bbox_dlf过程的无需修正")


def test_for_import_model_by_layers():
    # 测试按照层来导入模型参数

    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    import torch.optim as optim

    # 定义模型
    class TheModelClass(nn.Module):
        def __init__(self):
            super(TheModelClass, self).__init__()
            self.conv1 = nn.Conv2d(6, 6, 5)
            self.conv2 = nn.Conv2d(6, 6, 5)

        def forward(self, x):
            x = self.pool(F.relu(self.conv1(x)))
            x = self.pool(F.relu(self.conv2(x)))
            return x

    # 初始化模型
    model = TheModelClass()

    # 初始化优化器
    optimizer = optim.SGD(model.parameters(), lr=0.001, momentum=0.9)

    # # 打印模型的状态字典
    # print("Model's state_dict:")
    # for param_tensor in model.state_dict():
    #     print(param_tensor, "\t", model.state_dict()[param_tensor].size())
    #
    # # 打印优化器的状态字典
    # print("Optimizer's state_dict:")
    # for var_name in optimizer.state_dict():
    #     print(var_name, "\t", optimizer.state_dict()[var_name])

    # 保存conv1的值，作为conv2的预训练值
    save_state = {}
    print("Model's state_dict:")
    for param_tensor in model.state_dict():
        if 'conv1' in param_tensor:
            save_state.update({param_tensor.replace('1', '2', 1): model.state_dict()[param_tensor]})
            if "bias" in param_tensor:
                print("保存的bias为：", model.state_dict()[param_tensor])

    PATH = './test_for_save.pth'
    torch.save(save_state, PATH)

    model = TheModelClass()  # 首先通过代码获取模型结构
    model.load_state_dict(torch.load(PATH), strict=False)  # strict为False表示可以只导入部分层，而无需整个模型都导入
    print("加载的conv1的bias为：", model.state_dict()["conv1.bias"])
    print("加载的conv2的bias为：", model.state_dict()["conv2.bias"])
    print("综上，加载的是conv2\n")

    layer_number = "model2.model.2.cv2.bn.bias".split('.')[2]
    print("替换前：" + "model2.model.2.cv2.bn.bias")
    print("替换后：" + "model2.model.2.cv2.bn.bias".replace("." + layer_number + ".", "." + '3' + ".", 1))
    print("综上，可以通过替换层数的方式构造新层")


def test_for_exchange_teacher(path_teacher, path_teacher_prepare):
    """
    修复版本：正确转换教师模型权重到知识蒸馏模型格式
    """
    print(f"🔄 开始转换教师模型权重: {path_teacher}")
    save_state = {}
    model_teacher = YOLO(path_teacher)
    teacher_state_dict = model_teacher.model.state_dict()  # 使用model.state_dict()

    print(f"📊 教师模型参数数量: {len(teacher_state_dict)}")

    converted_count = 0
    skipped_count = 0

    for param_tensor in teacher_state_dict:
        try:
            # 去掉多余的model.前缀，统一格式
            clean_name = param_tensor
            if param_tensor.startswith('model.model.'):
                clean_name = param_tensor.replace('model.model.', 'model.', 1)
            elif param_tensor.startswith('model.'):
                clean_name = param_tensor
            else:
                clean_name = 'model.' + param_tensor

            # 检查参数名是否包含层数信息
            parts = clean_name.split('.')
            if len(parts) >= 3 and parts[2].isdigit():
                old_layer_number = int(parts[2])

                # 检查是否在教师层映射列表中
                if old_layer_number < len(teacher_peer_list):
                    new_layer_number = str(teacher_peer_list[old_layer_number])
                    new_param_name = clean_name.replace("." + str(old_layer_number) + ".", "." + new_layer_number + ".", 1)
                    save_state[new_param_name] = teacher_state_dict[param_tensor]
                    converted_count += 1
                else:
                    print(f"⚠️  层数超出映射范围: {clean_name}")
                    skipped_count += 1
            else:
                # 不是层参数，直接复制
                save_state[clean_name] = teacher_state_dict[param_tensor]
                converted_count += 1

        except Exception as e:
            print(f"❌ 处理参数 {param_tensor} 时出错: {e}")
            skipped_count += 1

    print(f"✅ 权重转换完成: {converted_count} 个参数转换, {skipped_count} 个参数跳过")
    print(f"📊 转换后参数数量: {len(save_state)}")

    torch.save(save_state, path_teacher_prepare)
    print(f"💾 教师权重已保存到: {path_teacher_prepare}")


def test_for_exchange_student(path_student, path_student_prepare):
    # 修改学生模型的预训练文件，用于构造新模型的预训练模型
    save_state = {}
    model_student = YOLO(path_student)
    for param_tensor in model_student.state_dict():
        old_layer_number = param_tensor.split('.')[2]
        new_layer_number = str(student_peer_list[int(old_layer_number)])
        save_state.update({param_tensor.replace("." + old_layer_number + ".", "." + new_layer_number + ".", 1):
                               model_student.state_dict()[param_tensor]})
    torch.save(save_state, path_student_prepare)
    return path_student_prepare


def test_for_exchange_student_back(model_based_train, model_after_train, path_save):
    """
    修复版本：更安全的模型权重转换函数
    """
    save_state = {}
    model_student = YOLO(model_based_train)

    print("🔄 开始模型权重转换...")
    print(f"📊 原模型参数数量: {len(model_after_train.state_dict())}")

    # 获取目标模型的state_dict作为参考
    target_state_dict = model_student.model.state_dict()
    source_state_dict = model_after_train.state_dict()

    converted_count = 0
    skipped_count = 0

    for param_tensor in source_state_dict:
        try:
            # 检查参数名是否包含层数信息
            parts = param_tensor.split('.')
            if len(parts) >= 3 and parts[2].isdigit():
                old_layer_number = int(parts[2])

                if old_layer_number in student_peer_list:
                    new_layer_number = str(student_peer_list.index(old_layer_number))
                    new_param_name = param_tensor.replace("." + str(old_layer_number) + ".", "." + new_layer_number + ".", 1)

                    # 检查目标模型是否有这个参数
                    if new_param_name in target_state_dict:
                        # 检查形状是否匹配
                        source_shape = source_state_dict[param_tensor].shape
                        target_shape = target_state_dict[new_param_name].shape

                        if source_shape == target_shape:
                            save_state[new_param_name] = source_state_dict[param_tensor]
                            converted_count += 1
                        else:
                            print(f"⚠️  形状不匹配: {param_tensor} {source_shape} -> {new_param_name} {target_shape}")
                            skipped_count += 1
                    else:
                        print(f"⚠️  目标模型中未找到参数: {new_param_name}")
                        skipped_count += 1
                else:
                    # 层数不在映射列表中，跳过
                    skipped_count += 1
            else:
                # 不是层参数，直接复制（如果存在）
                if param_tensor in target_state_dict:
                    source_shape = source_state_dict[param_tensor].shape
                    target_shape = target_state_dict[param_tensor].shape

                    if source_shape == target_shape:
                        save_state[param_tensor] = source_state_dict[param_tensor]
                        converted_count += 1
                    else:
                        print(f"⚠️  形状不匹配: {param_tensor} {source_shape} -> {target_shape}")
                        skipped_count += 1
                else:
                    skipped_count += 1

        except Exception as e:
            print(f"❌ 处理参数 {param_tensor} 时出错: {e}")
            skipped_count += 1

    print(f"✅ 转换完成: {converted_count} 个参数成功转换, {skipped_count} 个参数跳过")
    print(f"📊 转换后参数数量: {len(save_state)}")

    # 使用strict=False加载权重
    try:
        model_student.model.load_state_dict(save_state, strict=False)
        model_student.save(path_save)
        print(f"💾 模型已保存到: {path_save}")
    except Exception as e:
        print(f"❌ 保存模型时出错: {e}")
        # 尝试保存为torch格式
        torch.save(save_state, path_save.replace('.pt', '_weights.pth'))
        print(f"💾 权重已保存为: {path_save.replace('.pt', '_weights.pth')}")
        raise e


def test_for_transform_from_pt_to_onnx(final):
    # 注意要先在head.py和block.py两个文件内修改标记的5处代码，否则ncnn无法正常执行功能；如果只转onnx则无需这一步
    model = YOLO(final)
    model.export(format="onnx", imgsz=320, half=True, opset=12)


def test_for_transform_from_onnx_to_onnxsim(final):
    import onnx
    from onnxsim import simplify
    onnx_model = onnx.load(final)  # load onnx model
    model_simp, check = simplify(onnx_model)
    assert check, "Simplified ONNX model could not be validated"
    onnx.save(model_simp, final)


def test_for_evalute(final):

    import cv2
    import numpy as np
    from torchvision import transforms

    # 加载 YOLOv8 模型
    model = YOLO(final)

    # 确保模型处于评估模式
    model.eval()

    # 加载本地图片
    image_path = '2.jpg'
    image = cv2.imread(image_path)

    # 将图片转换为 RGB 格式
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

    # 将图片转换为 PyTorch 张量
    transform = transforms.Compose([
        transforms.ToTensor(),  # 将图片转换为张量
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])  # 标准化
    ])

    # 应用转换
    image_tensor = transform(image)

    # 添加批量维度
    image_tensor = image_tensor.unsqueeze(0)

    print(image_tensor.size())

    # 将张量移动到 GPU
    dummy_input = image_tensor.to(torch.device('cuda'))

    model.to(torch.device('cuda'))  # 指定使用 CUDA GPU 设备

    # 创建 CUDA 事件对象
    starter, ender = torch.cuda.Event(enable_timing=True), torch.cuda.Event(enable_timing=True)

    # 设置重复次数
    repetitions = 300
    timings = np.zeros((repetitions, 1))

    # GPU-WARM-UP：预热 GPU
    with torch.no_grad():
        for _ in range(10):
            _ = model(dummy_input)

    # MEASURE PERFORMANCE
    with torch.no_grad():
        for rep in range(repetitions):
            starter.record()
            _ = model(dummy_input)
            ender.record()
            # 等待 GPU 同步
            torch.cuda.synchronize()
            curr_time = starter.elapsed_time(ender)
            timings[rep] = curr_time

        # 计算平均推理时间和标准差
        mean_time = np.mean(timings)
        std_time = np.std(timings)
        # 打印输出结果
        print("Mean Inference Time: {:.4f} ms".format(mean_time))
        print("Standard Deviation: {:.4f} ms".format(std_time))
        # 计算吞吐量
        Throughput = (repetitions * batch_size) / np.sum(timings)
        print('Final Throughput:', Throughput)


if __name__ == '__main__':
    # [1]测试将预训练文件转化为预加载状态
    # test_for_exchange_student(path_student, path_student_prepare)
    # test_for_exchange_teacher(path_teacher, path_teacher_prepare)
    # [2]测试将训练结果转化为正常格式
    # model = YOLO("epoch160.pt")
    # test_for_exchange_student_back(model_based_file, model, final)
    # [3]测试正常格式的训练结果是否功能正常
    # model = YOLO(final)
    # model.val(data=dataset)
    # results = model(test_graph)
    # [4]测试将训练出的模型转化为onnx格式
    # test_for_transform_from_pt_to_onnx(final)
    # test_for_transform_from_onnx_to_onnxsim('epoch299.onnx')
    # test_for_evalute(final)
    test_for_cls_and_iou_and_dlf()
    print('test_for_function ends')
