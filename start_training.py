#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 启动增强知识蒸馏训练 - 简化版本
专注于提升MAP性能，回到成功的fruit数据集
"""

import os
import sys
import torch
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

def main():
    """主训练函数"""
    print("🍎 启动增强知识蒸馏训练")
    print("=" * 60)
    
    # 检查基本配置
    try:
        import Globals
        print(f"✅ 配置加载成功")
        print(f"📊 数据集: {Globals.dataset}")
        print(f"🏗️ 模型: {Globals.model_file}")
        print(f"🔄 训练轮数: {Globals.epochs}")
        print(f"🌡️ 温度参数: {Globals.hyp_T}")
        print(f"🎯 预期MAP@0.5: > 97%")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False
    
    # 检查CUDA
    if torch.cuda.is_available():
        print(f"🚀 使用GPU: {torch.cuda.get_device_name(0)}")
        torch.cuda.empty_cache()
    else:
        print("⚠️ 使用CPU训练")
    
    # 导入YOLO
    try:
        from ultralytics import YOLO
        print("✅ YOLO导入成功")
    except Exception as e:
        print(f"❌ YOLO导入失败: {e}")
        return False
    
    # 创建模型
    try:
        print("🏗️ 创建YOLO模型...")
        model = YOLO(Globals.model_file)
        print("✅ 模型创建成功")
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        return False
    
    # 训练参数
    train_args = {
        'data': Globals.dataset,
        'epochs': Globals.epochs,
        'batch': Globals.batch_size,
        'device': Globals.device,
        'save': Globals.bool_save,
        'save_period': Globals.save_period,
        'resume': Globals.bool_resume,
        'project': 'runs/detect',
        'name': f'enhanced_fruit_distill_T{Globals.hyp_T}_ep{Globals.epochs}',
        'exist_ok': True,
        'pretrained': True,
        'optimizer': 'AdamW',
        'lr0': 0.001,
        'lrf': 0.01,
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3.0,
        'warmup_momentum': 0.8,
        'warmup_bias_lr': 0.1,
        'box': 7.5,
        'cls': 0.5,
        'dfl': 1.5,
        'label_smoothing': Globals.label_smoothing,
        'val': True,
        'plots': True,
        'verbose': True,
        'seed': 42,
        'deterministic': True,
        'workers': 4,  # 减少worker数量
        'amp': True,
        'fraction': 1.0,
        'profile': False,
        'patience': 50,
        'close_mosaic': 10,
    }
    
    print("\n🚀 开始训练...")
    print("📊 训练参数:")
    for key, value in train_args.items():
        print(f"  {key}: {value}")
    
    try:
        # 开始训练
        results = model.train(**train_args)
        
        print("\n✅ 训练完成!")
        print(f"📈 训练结果: {results}")
        
        # 保存模型
        if hasattr(model, 'model'):
            torch.save(model.model.state_dict(), Globals.final)
            print(f"💾 模型已保存: {Globals.final}")
        
        # 验证模型
        print("\n🔍 验证模型...")
        val_results = model.val(data=Globals.dataset, device=Globals.device)
        
        print("📊 最终验证结果:")
        print(f"  🎯 mAP@0.5: {val_results.box.map50:.4f}")
        print(f"  📈 mAP@0.5:0.95: {val_results.box.map:.4f}")
        print(f"  🎯 Precision: {val_results.box.mp:.4f}")
        print(f"  📊 Recall: {val_results.box.mr:.4f}")
        
        # 检查性能
        if val_results.box.map50 > 0.97:
            print("🏆 优秀! MAP@0.5 > 97%")
        elif val_results.box.map50 > 0.95:
            print("👍 良好! MAP@0.5 > 95%")
        else:
            print("⚠️ 需要进一步优化")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 增强知识蒸馏训练成功完成!")
        print("🍎 水果检测模型已优化，MAP性能提升!")
    else:
        print("\n💥 训练失败，请检查配置!")
        sys.exit(1)
